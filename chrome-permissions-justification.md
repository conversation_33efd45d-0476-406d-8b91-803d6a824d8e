# SnapAny Extension - Chrome商店权限说明文档

## 权限概述

SnapAny Extension 是一个专业的媒体文件检测和下载工具，需要以下权限来实现其核心功能：

---

## 1. sidePanel

**权限作用：** 提供侧边栏界面功能

**英文justification：**
This permission enables the extension to provide a side panel interface as an alternative to the popup window. Users can choose between popup and side panel modes for better user experience. The side panel provides more space for displaying detected media files and offers a more convenient workflow for managing downloads.

**中文含义说明：**
此权限用于提供侧边栏界面功能，作为弹出窗口的替代方案。用户可以在弹出窗口和侧边栏模式之间切换，获得更好的用户体验。侧边栏提供更大的空间来显示检测到的媒体文件，并为管理下载提供更便捷的工作流程。

**具体实现功能：**
- 在侧边栏中显示检测到的媒体文件列表
- 提供与弹出窗口相同的功能但具有更大的显示空间
- 支持用户在弹出窗口和侧边栏模式之间切换
- 保存用户的界面偏好设置

---

## 2. webRequest

**权限作用：** 监控和拦截网络请求

**英文justification：**
This permission is essential for monitoring network requests to detect media files (videos, audio, images) on web pages. The extension analyzes HTTP requests to identify downloadable media content, extract file information (size, type, headers), and provide users with a comprehensive list of available media files. This is the core functionality that enables automatic media detection.

**中文含义说明：**
此权限是监控网络请求以检测网页上媒体文件（视频、音频、图片）的核心功能。扩展分析HTTP请求来识别可下载的媒体内容，提取文件信息（大小、类型、请求头），并为用户提供可用媒体文件的完整列表。这是实现自动媒体检测的核心功能。

**具体实现功能：**
- 监听所有网络请求（onBeforeRequest, onBeforeSendHeaders, onHeadersReceived, onCompleted）
- 检测和过滤媒体文件请求（视频、音频、图片等）
- 提取请求和响应头信息用于下载时携带
- 获取文件大小、类型等元数据信息
- 支持M3U8流媒体文件的特殊处理

---

## 3. tabs

**权限作用：** 访问和管理浏览器标签页信息

**英文justification：**
This permission allows the extension to access tab information such as title, URL, and favicon, which are necessary for organizing detected media files by their source pages. It also enables the extension to manage tab-specific data, clean up resources when tabs are closed, and provide context about where media files were found.

**中文含义说明：**
此权限允许扩展访问标签页信息，如标题、URL和图标，这些信息对于按来源页面组织检测到的媒体文件是必要的。它还使扩展能够管理特定标签页的数据，在标签页关闭时清理资源，并提供媒体文件来源的上下文信息。

**具体实现功能：**
- 获取标签页标题、URL和图标信息
- 按标签页组织和隔离媒体文件数据
- 监听标签页关闭事件，自动清理相关数据
- 获取当前活动标签页信息
- 向特定标签页发送消息通知

---

## 4. activeTab

**权限作用：** 访问当前活动标签页

**英文justification：**
This permission provides access to the currently active tab, which is necessary for displaying relevant media files detected on the current page. It ensures that users see media content specific to the tab they are currently viewing, improving the user experience by showing contextually relevant information.

**中文含义说明：**
此权限提供对当前活动标签页的访问，这对于显示在当前页面检测到的相关媒体文件是必要的。它确保用户看到特定于他们当前查看的标签页的媒体内容，通过显示上下文相关信息来改善用户体验。

**具体实现功能：**
- 识别当前活动的标签页
- 显示当前页面检测到的媒体文件
- 更新扩展图标角标显示当前页面的媒体文件数量
- 提供基于当前页面的上下文功能

---

## 5. storage

**权限作用：** 本地数据存储

**英文justification：**
This permission enables the extension to store user preferences, download data, and temporary information locally. It's used to save user interface preferences (popup vs side panel), cache download information for external downloader integration, and maintain extension settings across browser sessions.

**中文含义说明：**
此权限使扩展能够在本地存储用户偏好、下载数据和临时信息。它用于保存用户界面偏好（弹出窗口vs侧边栏），缓存下载信息以便与外部下载器集成，并在浏览器会话之间维护扩展设置。

**具体实现功能：**
- 保存用户的界面模式偏好（popup/sidepanel）
- 存储下载任务数据供外部下载器使用
- 缓存媒体文件信息和元数据
- 保存扩展配置和用户设置
- 临时存储请求头信息用于下载

---

## 6. declarativeNetRequest

**权限作用：** 动态修改网络请求头

**英文justification：**
This permission is crucial for modifying HTTP request headers to enable proper downloading of media files that require specific headers (like Referer, Origin, or authentication headers). It allows the extension to bypass CORS restrictions and ensure successful downloads by injecting necessary headers dynamically, especially for M3U8 streaming media and protected content.

**中文含义说明：**
此权限对于修改HTTP请求头以实现需要特定头部（如Referer、Origin或认证头部）的媒体文件的正确下载至关重要。它允许扩展绕过CORS限制，通过动态注入必要的头部来确保成功下载，特别是对于M3U8流媒体和受保护的内容。

**具体实现功能：**
- 为下载请求动态添加必要的HTTP头部
- 设置Referer和Origin头部以绕过防盗链
- 支持M3U8流媒体的特殊请求头处理
- 实现CORS绕过功能
- 为扩展页面预览功能注入请求头

---

## 7. downloads

**权限作用：** 管理浏览器下载功能

**英文justification：**
This permission allows the extension to integrate with the browser's download system for initiating file downloads. While the extension primarily uses an external downloader for advanced features, this permission provides fallback download capabilities and enables better integration with the browser's native download management.

**中文含义说明：**
此权限允许扩展与浏览器的下载系统集成以启动文件下载。虽然扩展主要使用外部下载器来实现高级功能，但此权限提供备用下载功能，并能够更好地与浏览器的原生下载管理集成。

**具体实现功能：**
- 提供备用的浏览器原生下载功能
- 与浏览器下载管理器集成
- 支持简单文件的直接下载
- 为复杂下载场景提供基础下载能力

---

## 8. scripting

**权限作用：** 向网页注入脚本

**英文justification：**
This permission enables the extension to inject content scripts into web pages for communication with external downloader pages. It's specifically used to establish a bridge between the extension and the external downloader interface, allowing seamless data transfer and download management through a dedicated web interface.

**中文含义说明：**
此权限使扩展能够向网页注入内容脚本，以便与外部下载器页面进行通信。它专门用于在扩展和外部下载器界面之间建立桥梁，允许通过专用的Web界面进行无缝的数据传输和下载管理。

**具体实现功能：**
- 向外部下载器页面注入通信脚本
- 建立扩展与外部下载器的数据桥梁
- 传递下载任务和请求头信息
- 实现扩展与Web页面的双向通信

---

## 9. Host permission (https://*/*, http:///*/*)

**权限作用：** 访问所有网站

**英文justification：**
This broad host permission is necessary because the extension needs to detect media files on any website that users visit. Since users may want to download media from any domain on the internet, the extension requires access to all websites to monitor network requests and detect downloadable content universally.

**中文含义说明：**
这个广泛的主机权限是必要的，因为扩展需要在用户访问的任何网站上检测媒体文件。由于用户可能想要从互联网上的任何域名下载媒体，扩展需要访问所有网站来监控网络请求并普遍检测可下载的内容。

**具体实现功能：**
- 在任何网站上监控网络请求
- 检测来自任何域名的媒体文件
- 支持跨域媒体文件的下载
- 为所有网站提供统一的媒体检测功能
- 确保扩展在任何网站上都能正常工作

---

## 总结

SnapAny Extension 的所有权限都是为了实现其核心功能：**自动检测和下载网页媒体文件**。每个权限都有明确的用途和实现目标，确保用户能够便捷、安全地管理和下载网络媒体内容。扩展严格按照最小权限原则使用这些权限，仅在必要时访问相关功能，保护用户隐私和数据安全。